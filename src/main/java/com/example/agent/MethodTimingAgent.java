package com.example.agent;

import java.lang.instrument.Instrumentation;

/**
 * Java Agent主类 - 方法执行时间监控
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class MethodTimingAgent {
    
    /**
     * JVM启动时调用的premain方法
     * 
     * @param agentArgs Agent启动参数
     * @param inst Instrumentation实例
     */
    public static void premain(String agentArgs, Instrumentation inst) {
        System.out.println("=== Java Agent 已启动 ===");
        System.out.println("Agent参数: " + agentArgs);
        System.out.println("JVM版本: " + System.getProperty("java.version"));
        
        // 注册字节码转换器
        inst.addTransformer(new MethodTimingTransformer());
        
        System.out.println("字节码转换器已注册");
    }
    
    /**
     * 运行时attach调用的agentmain方法
     * 
     * @param agentArgs Agent启动参数
     * @param inst Instrumentation实例
     */
    public static void agentmain(String agentArgs, Instrumentation inst) {
        System.out.println("=== Java Agent 运行时附加 ===");
        premain(agentArgs, inst);
    }
}
