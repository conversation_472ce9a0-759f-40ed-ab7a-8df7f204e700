package com.example.agent;

import java.util.Random;

/**
 * 测试服务类 - 用于演示Agent监控功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class TestService {
    
    private Random random = new Random();
    
    /**
     * 快速方法 - 执行时间很短
     */
    public void quickMethod() {
        System.out.println("执行快速方法");
    }
    
    /**
     * 慢速方法 - 模拟耗时操作
     */
    public void slowMethod() {
        try {
            // 随机睡眠20-100ms
            int sleepTime = 20 + random.nextInt(80);
            Thread.sleep(sleepTime);
            System.out.println("执行慢速方法，耗时: " + sleepTime + "ms");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("方法执行被中断");
        }
    }
    
    /**
     * 计算方法 - 模拟CPU密集型操作
     */
    public String calculateSomething(int n) {
        try {
            Thread.sleep(10); // 模拟一些IO操作
            
            // CPU密集型计算
            long result = 0;
            for (int i = 0; i < n; i++) {
                result += i * i;
            }
            
            return "计算结果: " + result;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return "计算被中断";
        }
    }
    
    /**
     * 数据库查询模拟方法
     */
    public String queryDatabase(String sql) {
        try {
            // 模拟数据库查询延迟
            int queryTime = 30 + random.nextInt(50);
            Thread.sleep(queryTime);
            
            return "查询结果: " + sql + " (模拟数据)";
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return "查询被中断";
        }
    }
    
    /**
     * 网络请求模拟方法
     */
    public String httpRequest(String url) {
        try {
            // 模拟网络请求延迟
            int requestTime = 50 + random.nextInt(100);
            Thread.sleep(requestTime);
            
            return "HTTP响应: " + url + " (状态码: 200)";
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return "请求超时";
        }
    }
}
