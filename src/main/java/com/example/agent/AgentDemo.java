package com.example.agent;

/**
 * Agent演示主类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class AgentDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Java Agent Demo 开始 ===");
        System.out.println("当前时间: " + new java.util.Date());
        
        TestService service = new TestService();
        
        try {
            // 1. 测试快速方法
            System.out.println("\n--- 测试快速方法 ---");
            service.quickMethod();
            
            // 2. 测试慢速方法
            System.out.println("\n--- 测试慢速方法 ---");
            for (int i = 0; i < 3; i++) {
                service.slowMethod();
            }
            
            // 3. 测试计算方法
            System.out.println("\n--- 测试计算方法 ---");
            String result1 = service.calculateSomething(10000);
            System.out.println(result1);
            
            String result2 = service.calculateSomething(50000);
            System.out.println(result2);
            
            // 4. 测试数据库查询方法
            System.out.println("\n--- 测试数据库查询 ---");
            String dbResult = service.queryDatabase("SELECT * FROM users WHERE id = 1");
            System.out.println(dbResult);
            
            // 5. 测试网络请求方法
            System.out.println("\n--- 测试网络请求 ---");
            String httpResult = service.httpRequest("https://api.example.com/data");
            System.out.println(httpResult);
            
            // 6. 批量测试观察统计效果
            System.out.println("\n--- 批量测试 ---");
            for (int i = 0; i < 5; i++) {
                service.slowMethod();
                if (i % 2 == 0) {
                    service.calculateSomething(1000 * (i + 1));
                }
            }
            
        } catch (Exception e) {
            System.err.println("演示过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n=== Demo 结束 ===");
    }
}
