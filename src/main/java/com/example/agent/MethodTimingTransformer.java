package com.example.agent;

import javassist.*;
import java.lang.instrument.ClassFileTransformer;
import java.security.ProtectionDomain;

/**
 * 字节码转换器 - 使用Javassist进行字节码操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class MethodTimingTransformer implements ClassFileTransformer {
    
    private static final String[] EXCLUDE_PREFIXES = {
        "java/", "javax/", "sun/", "com/sun/", "jdk/",
        "javassist/", "org/objectweb/asm/", "net/bytebuddy/"
    };
    
    @Override
    public byte[] transform(ClassLoader loader, String className, Class<?> classBeingRedefined,
                          ProtectionDomain protectionDomain, byte[] classfileBuffer) {
        
        // 过滤系统类和第三方库类
        if (shouldExcludeClass(className)) {
            return null;
        }
        
        try {
            return transformClass(className);
        } catch (Exception e) {
            System.err.println("转换类失败: " + className + ", 错误: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 判断是否应该排除该类
     */
    private boolean shouldExcludeClass(String className) {
        if (className == null) {
            return true;
        }
        
        // 排除系统类和第三方库
        for (String prefix : EXCLUDE_PREFIXES) {
            if (className.startsWith(prefix)) {
                return true;
            }
        }
        
        // 排除Agent本身的类
        if (className.contains("Agent") || className.contains("Transformer")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 转换类字节码
     */
    private byte[] transformClass(String className) throws Exception {
        ClassPool pool = ClassPool.getDefault();
        CtClass ctClass = pool.get(className.replace("/", "."));
        
        // 获取类中的所有方法
        CtMethod[] methods = ctClass.getDeclaredMethods();
        boolean hasEnhanced = false;
        
        for (CtMethod method : methods) {
            if (shouldEnhanceMethod(method)) {
                enhanceMethod(method);
                hasEnhanced = true;
            }
        }
        
        if (hasEnhanced) {
            byte[] result = ctClass.toBytecode();
            ctClass.detach(); // 释放内存
            System.out.println("已增强类: " + className);
            return result;
        }
        
        ctClass.detach();
        return null;
    }
    
    /**
     * 判断是否应该增强该方法
     */
    private boolean shouldEnhanceMethod(CtMethod method) throws Exception {
        int modifiers = method.getModifiers();
        
        // 跳过抽象方法、native方法、构造方法
        if (Modifier.isAbstract(modifiers) || 
            Modifier.isNative(modifiers) ||
            method.getName().equals("<init>") ||
            method.getName().equals("<clinit>")) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 增强方法 - 添加执行时间统计
     */
    private void enhanceMethod(CtMethod method) throws Exception {
        String methodName = method.getName();
        String className = method.getDeclaringClass().getName();
        String uniqueId = String.valueOf(Math.abs((className + methodName).hashCode()));
        
        // 在方法开始处插入代码
        String startCode = String.format(
            "long startTime_%s = System.currentTimeMillis();", 
            uniqueId
        );
        
        // 在方法结束处插入代码
        String endCode = String.format(
            "{ " +
            "long endTime_%1$s = System.currentTimeMillis(); " +
            "long duration_%1$s = endTime_%1$s - startTime_%1$s; " +
            "if (duration_%1$s > 5) { " + // 只记录超过5ms的方法
            "  System.out.println(\"[Agent监控] \" + \"%2$s.%3$s() 执行时间: \" + duration_%1$s + \"ms\"); " +
            "} " +
            "}",
            uniqueId, className, methodName
        );
        
        method.insertBefore(startCode);
        method.insertAfter(endCode, true); // true表示在finally块中执行
    }
}
