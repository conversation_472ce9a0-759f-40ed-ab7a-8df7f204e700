#!/bin/bash

echo "=== 运行Java Agent Demo ==="

# 返回项目根目录
cd "$(dirname "$0")/.."

# 检查JAR文件是否存在
JAR_FILE="target/java-agent-demo-1.0.0.jar"

if [ ! -f "$JAR_FILE" ]; then
    echo "错误: JAR文件不存在，请先运行构建脚本"
    echo "执行: ./run-scripts/build.sh"
    exit 1
fi

# 运行Agent Demo
echo "启动应用..."
echo "命令: java -javaagent:$JAR_FILE -cp $JAR_FILE com.example.agent.AgentDemo"
echo ""

java -javaagent:$JAR_FILE -cp $JAR_FILE com.example.agent.AgentDemo

echo ""
echo "=== 运行完成 ==="
