@echo off
echo === 构建Java Agent项目 ===

rem 检查Maven是否安装
where mvn >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Maven未安装或不在PATH中
    echo 请安装Maven: https://maven.apache.org/install.html
    pause
    exit /b 1
)

rem 返回项目根目录
cd /d "%~dp0\.."

rem 清理并编译项目
echo 清理项目...
mvn clean

echo 编译和打包项目...
mvn package

rem 检查构建结果
if exist "target\java-agent-demo-1.0.0.jar" (
    echo ✅ 构建成功!
    echo 生成的JAR文件: target\java-agent-demo-1.0.0.jar
    echo.
    echo 使用方法:
    echo java -javaagent:target\java-agent-demo-1.0.0.jar -cp target\java-agent-demo-1.0.0.jar com.example.agent.AgentDemo
) else (
    echo ❌ 构建失败!
    pause
    exit /b 1
)

pause
