@echo off
echo === 运行Java Agent Demo ===

rem 返回项目根目录
cd /d "%~dp0\.."

rem 检查JAR文件是否存在
set JAR_FILE=target\java-agent-demo-1.0.0.jar

if not exist "%JAR_FILE%" (
    echo 错误: JAR文件不存在，请先运行构建脚本
    echo 执行: run-scripts\build.bat
    pause
    exit /b 1
)

rem 运行Agent Demo
echo 启动应用...
echo 命令: java -javaagent:%JAR_FILE% -cp %JAR_FILE% com.example.agent.AgentDemo
echo.

java -javaagent:%JAR_FILE% -cp %JAR_FILE% com.example.agent.AgentDemo

echo.
echo === 运行完成 ===
pause
