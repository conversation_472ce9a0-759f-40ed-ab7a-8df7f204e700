#!/bin/bash

echo "=== 构建Java Agent项目 ==="

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "错误: Maven未安装或不在PATH中"
    echo "请安装Maven: https://maven.apache.org/install.html"
    exit 1
fi

# 返回项目根目录
cd "$(dirname "$0")/.."

# 清理并编译项目
echo "清理项目..."
mvn clean

echo "编译和打包项目..."
mvn package

# 检查构建结果
if [ -f "target/java-agent-demo-1.0.0.jar" ]; then
    echo "✅ 构建成功!"
    echo "生成的JAR文件: target/java-agent-demo-1.0.0.jar"
    
    # 显示JAR文件信息
    echo ""
    echo "JAR文件信息:"
    ls -lh target/java-agent-demo-1.0.0.jar
    
    echo ""
    echo "使用方法:"
    echo "java -javaagent:target/java-agent-demo-1.0.0.jar -cp target/java-agent-demo-1.0.0.jar com.example.agent.AgentDemo"
else
    echo "❌ 构建失败!"
    exit 1
fi
