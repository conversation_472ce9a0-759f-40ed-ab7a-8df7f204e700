Java Agent Demo 运行说明
========================

系统要求:
- JDK 8 或更高版本
- Maven 3.6 或更高版本
- 网络连接(首次构建时下载依赖)

Linux/Mac 使用方法:
1. 构建项目: ./run-scripts/build.sh
2. 运行Demo: ./run-scripts/run.sh

Windows 使用方法:
1. 构建项目: 双击 run-scripts/build.bat
2. 运行Demo: 双击 run-scripts/run.bat

手动运行命令:
java -javaagent:target/java-agent-demo-1.0.0.jar -cp target/java-agent-demo-1.0.0.jar com.example.agent.AgentDemo

预期输出:
- Agent启动信息
- 字节码增强日志  
- 方法执行时间监控(>5ms)
- 各种测试场景的结果

注意事项:
- 首次构建需要下载Maven依赖，请确保网络连接正常
- Agent只监控执行时间超过5ms的方法
- 系统类和第三方库类会被自动过滤
