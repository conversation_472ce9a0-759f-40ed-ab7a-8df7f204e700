# Java Agent Demo

一个完整的Java Agent演示项目，展示如何使用Instrumentation API和Javassist进行方法执行时间监控。

## 🚀 功能特性

- ✅ 方法执行时间自动监控
- ✅ 字节码动态增强
- ✅ 智能过滤系统类
- ✅ 支持启动时和运行时附加
- ✅ Maven标准项目结构
- ✅ 跨平台支持(Windows/Linux/Mac)

## 📋 系统要求

- JDK 8 或更高版本
- Maven 3.6 或更高版本
- 网络连接(首次构建时下载依赖)

## 🎯 快速开始

### Linux/Mac 系统

```bash
# 1. 构建项目
./run-scripts/build.sh

# 2. 运行演示
./run-scripts/run.sh
```

### Windows 系统

```batch
# 1. 构建项目
双击 run-scripts/build.bat

# 2. 运行演示  
双击 run-scripts/run.bat
```

### 手动运行

```bash
# 构建
mvn clean package

# 运行
java -javaagent:target/java-agent-demo-1.0.0.jar \
     -cp target/java-agent-demo-1.0.0.jar \
     com.example.agent.AgentDemo
```

## 📁 项目结构

```
java-agent-demo/
├── pom.xml                                    # Maven配置
├── README.md                                  # 项目说明
├── src/main/java/com/example/agent/
│   ├── MethodTimingAgent.java                 # Agent主类
│   ├── MethodTimingTransformer.java           # 字节码转换器
│   ├── TestService.java                       # 测试服务
│   └── AgentDemo.java                         # 演示主类
├── src/main/resources/META-INF/MANIFEST.MF    # Agent配置
└── run-scripts/                               # 运行脚本
    ├── build.sh / build.bat                   # 构建脚本
    ├── run.sh / run.bat                       # 运行脚本
    └── README.txt                             # 使用说明
```

## 🔧 核心技术

- **Java Instrumentation API**: JVM字节码操作接口
- **Javassist**: 字节码操作库，比ASM更易用
- **Maven Shade Plugin**: 依赖打包插件

## 📊 监控规则

- 只监控执行时间 > 5ms 的方法
- 自动过滤系统类和第三方库
- 支持异常安全的监控(finally块执行)
- 智能类名过滤，避免无限递归

## 📈 预期输出示例

```
=== Java Agent 已启动 ===
Agent参数: null
JVM版本: 11.0.12
字节码转换器已注册
已增强类: com/example/agent/TestService
=== Java Agent Demo 开始 ===

--- 测试慢速方法 ---
[Agent监控] com.example.agent.TestService.slowMethod() 执行时间: 67ms
执行慢速方法，耗时: 67ms

--- 测试计算方法 ---
[Agent监控] com.example.agent.TestService.calculateSomething(int) 执行时间: 15ms
计算结果: 333283335000
```

## 🚧 扩展功能

基于此框架，您可以扩展更多功能：
- 方法参数和返回值记录
- 异常监控和统计
- 性能瓶颈分析
- 调用链追踪
- 内存使用监控
- 方法调用计数
- 热点方法识别

## 🐛 常见问题

**Q: 构建时出现网络连接错误？**
A: 首次构建需要下载Maven依赖，请确保网络连接正常，或配置Maven镜像。

**Q: Agent没有监控到某些方法？**
A: 检查方法执行时间是否超过5ms，或查看控制台是否有类过滤日志。

**Q: 如何修改监控阈值？**
A: 修改 MethodTimingTransformer.java 中的阈值条件(当前为5ms)。

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
